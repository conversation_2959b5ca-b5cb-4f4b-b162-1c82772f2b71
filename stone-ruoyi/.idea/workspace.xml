<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d02f2730-c545-4d7b-b89d-6c8145968889" name="更改" comment="修正抢购时间">
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/common/api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../stone-uniapp/common/api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/pages/order/orderDetail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../stone-uniapp/pages/order/orderDetail.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\environment\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2ueZvA5MJ5QqVfMtFt3FhaLL4id" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.ruoyi [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [org.apache.maven.plugins:maven-install-plugin:3.1.2:install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ApiApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.WebApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/projects/stone/stone-ruoyi&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.build:prod.executor&quot;: &quot;Run&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\IntelliJ IDEA 2024.3.4.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ApiApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="stonejava" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="stonejava" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="ApiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.ApiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.WebApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="build:prod" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/ruoyi-ui/package.json" />
      <command value="run" />
      <scripts>
        <script value="build:prod" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/ruoyi-ui/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.build:prod" />
      <item itemvalue="npm.dev" />
      <item itemvalue="Spring Boot.WebApplication" />
      <item itemvalue="Spring Boot.ApiApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.build:prod" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="d02f2730-c545-4d7b-b89d-6c8145968889" name="更改" comment="" />
      <created>1742611229064</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742611229064</updated>
      <workItem from="1742611230243" duration="3076000" />
      <workItem from="1742638356460" duration="34000" />
      <workItem from="1742790324232" duration="20000" />
      <workItem from="1742887228755" duration="877000" />
      <workItem from="1743143892697" duration="2327000" />
      <workItem from="1743146633890" duration="6530000" />
      <workItem from="1743399767708" duration="18516000" />
      <workItem from="1743469816182" duration="6700000" />
      <workItem from="1743493694154" duration="861000" />
      <workItem from="1743557420420" duration="3894000" />
      <workItem from="1743565498485" duration="7000" />
      <workItem from="1743576933054" duration="176000" />
      <workItem from="1743581552893" duration="618000" />
      <workItem from="1744161620539" duration="2083000" />
      <workItem from="1744252848716" duration="1673000" />
      <workItem from="1744690854906" duration="5781000" />
      <workItem from="1744699835470" duration="248000" />
      <workItem from="1744852327911" duration="4143000" />
      <workItem from="1745199167275" duration="1366000" />
      <workItem from="1745205488243" duration="551000" />
      <workItem from="1745289179293" duration="2791000" />
      <workItem from="1745461895141" duration="9333000" />
      <workItem from="1745548977006" duration="9737000" />
      <workItem from="1745993921909" duration="17000" />
      <workItem from="1745999194828" duration="51000" />
      <workItem from="1746864690309" duration="71000" />
      <workItem from="1746869193320" duration="4635000" />
      <workItem from="1747185817516" duration="3326000" />
      <workItem from="1750561579626" duration="1373000" />
      <workItem from="1750993420355" duration="2274000" />
      <workItem from="1751002103066" duration="1975000" />
      <workItem from="1751030655659" duration="2598000" />
      <workItem from="1751158112050" duration="1821000" />
      <workItem from="1754726713739" duration="2557000" />
      <workItem from="1754753315586" duration="1864000" />
      <workItem from="1754755714093" duration="8374000" />
      <workItem from="1754830804827" duration="893000" />
      <workItem from="1754832022711" duration="470000" />
    </task>
    <task id="LOCAL-00001" summary="石头">
      <option name="closed" value="true" />
      <created>1742611898395</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1742611898395</updated>
    </task>
    <task id="LOCAL-00002" summary="石头">
      <option name="closed" value="true" />
      <created>1743145330111</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1743145330111</updated>
    </task>
    <task id="LOCAL-00003" summary="石头修改（后端加抢购明细）">
      <option name="closed" value="true" />
      <created>1744164414483</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1744164414483</updated>
    </task>
    <task id="LOCAL-00004" summary="修正抢购时间">
      <option name="closed" value="true" />
      <created>1745205991627</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1745205991627</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="石头" />
    <MESSAGE value="石头修改（后端加抢购明细）" />
    <MESSAGE value="修正抢购时间" />
    <option name="LAST_COMMIT_MESSAGE" value="修正抢购时间" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ruoyi-api/src/main/java/com/ruoyi/api/controller/ApiHtGoodsController.java</url>
          <line>120</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>