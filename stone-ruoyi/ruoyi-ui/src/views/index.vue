<template>
  <div class="welcome-container">
    <!-- 主要欢迎区域 -->
    <div class="welcome-main">
      <div class="welcome-content">
        <div class="logo-section">
          <div class="logo-icon">
            <i class="el-icon-shopping-bag-2"></i>
          </div>
          <h1 class="welcome-title">永意香商城</h1>
          <p class="welcome-subtitle">管理后台系统</p>
        </div>

        <div class="welcome-message">
          <h2>欢迎登陆永意香商城</h2>
          <p class="message-text">您已成功登陆永意香商城管理后台，开始您的商城管理之旅</p>
        </div>

        <div class="quick-actions">
          <h3>快速操作</h3>
          <div class="action-cards">
            <div class="action-card" @click="navigateTo('/system/goods')">
              <i class="el-icon-goods"></i>
              <span>商品管理</span>
            </div>
            <div class="action-card" @click="navigateTo('/order/order')">
              <i class="el-icon-document"></i>
              <span>订单管理</span>
            </div>
            <div class="action-card" @click="navigateTo('/system/user')">
              <i class="el-icon-user"></i>
              <span>用户管理</span>
            </div>
            <div class="action-card" @click="navigateTo('/system/config')">
              <i class="el-icon-setting"></i>
              <span>系统设置</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统信息卡片 -->
    <div class="info-cards">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-time"></i>
              <span>当前时间</span>
            </div>
            <div class="card-content">
              <p class="current-time">{{ currentTime }}</p>
              <p class="current-date">{{ currentDate }}</p>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-user"></i>
              <span>登陆信息</span>
            </div>
            <div class="card-content">
              <p>用户名：{{ userInfo.userName || '管理员' }}</p>
              <p>角色：{{ userInfo.roleName || '系统管理员' }}</p>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-card">
            <div class="card-header">
              <i class="el-icon-monitor"></i>
              <span>系统状态</span>
            </div>
            <div class="card-content">
              <p>系统运行正常</p>
              <p class="status-good">服务状态良好</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Index',
  data() {
    return {
      currentTime: '',
      currentDate: '',
      timer: null,
      userInfo: {}
    }
  },
  mounted() {
    this.updateTime()
    this.timer = setInterval(this.updateTime, 1000)
    this.getUserInfo()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    // 更新当前时间
    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      this.currentDate = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    },

    // 获取用户信息
    getUserInfo() {
      // 从store或localStorage获取用户信息
      const userInfo = this.$store.getters.name || JSON.parse(localStorage.getItem('userInfo') || '{}')
      this.userInfo = {
        userName: userInfo.userName || userInfo.name || '管理员',
        roleName: userInfo.roleName || '系统管理员'
      }
    },

    // 导航到指定页面
    navigateTo(path) {
      this.$router.push(path).catch(err => {
        // 如果路由不存在，显示提示
        if (err.name === 'NavigationDuplicated') {
          return
        }
        this.$message.warning('该功能模块暂未开放，请联系管理员')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.welcome-container {
  min-height: calc(100vh - 84px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;

  .welcome-main {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);

    .welcome-content {
      text-align: center;

      .logo-section {
        margin-bottom: 40px;

        .logo-icon {
          font-size: 80px;
          color: #409EFF;
          margin-bottom: 20px;

          i {
            display: inline-block;
            animation: bounce 2s infinite;
          }
        }

        .welcome-title {
          font-size: 48px;
          font-weight: bold;
          color: #2c3e50;
          margin: 0 0 10px 0;
          background: linear-gradient(45deg, #409EFF, #67C23A);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .welcome-subtitle {
          font-size: 18px;
          color: #666;
          margin: 0;
        }
      }

      .welcome-message {
        margin-bottom: 40px;

        h2 {
          font-size: 32px;
          color: #2c3e50;
          margin-bottom: 15px;
          font-weight: 600;
        }

        .message-text {
          font-size: 16px;
          color: #666;
          line-height: 1.6;
        }
      }

      .quick-actions {
        h3 {
          font-size: 24px;
          color: #2c3e50;
          margin-bottom: 30px;
          font-weight: 600;
        }

        .action-cards {
          display: flex;
          justify-content: center;
          gap: 20px;
          flex-wrap: wrap;

          .action-card {
            background: linear-gradient(135deg, #409EFF, #36a3f7);
            color: white;
            padding: 30px 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
            box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);

            &:hover {
              transform: translateY(-5px);
              box-shadow: 0 15px 35px rgba(64, 158, 255, 0.4);
              background: linear-gradient(135deg, #36a3f7, #409EFF);
            }

            i {
              font-size: 32px;
              display: block;
              margin-bottom: 10px;
            }

            span {
              font-size: 14px;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .info-cards {
    .info-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      padding: 25px;
      height: 140px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        i {
          font-size: 20px;
          color: #409EFF;
          margin-right: 8px;
        }

        span {
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
        }
      }

      .card-content {
        p {
          margin: 8px 0;
          color: #666;
          font-size: 14px;

          &.current-time {
            font-size: 24px;
            font-weight: bold;
            color: #409EFF;
            margin-bottom: 5px;
          }

          &.current-date {
            font-size: 14px;
            color: #999;
          }

          &.status-good {
            color: #67C23A;
            font-weight: 500;
          }
        }
      }
    }
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

@media (max-width: 768px) {
  .welcome-container {
    padding: 10px;

    .welcome-main {
      padding: 20px;

      .welcome-content {
        .logo-section {
          .welcome-title {
            font-size: 32px;
          }
        }

        .welcome-message {
          h2 {
            font-size: 24px;
          }
        }

        .quick-actions {
          .action-cards {
            gap: 15px;

            .action-card {
              min-width: 100px;
              padding: 20px 15px;

              i {
                font-size: 24px;
              }
            }
          }
        }
      }
    }

    .info-cards {
      .info-card {
        height: auto;
        min-height: 120px;
      }
    }
  }
}
</style>
