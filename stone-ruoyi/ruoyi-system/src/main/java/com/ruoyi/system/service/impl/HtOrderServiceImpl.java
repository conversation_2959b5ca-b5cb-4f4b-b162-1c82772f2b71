package com.ruoyi.system.service.impl;

import com.alipay.api.domain.Goods;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.domain.HtUserShares;
import com.ruoyi.system.domain.vo.HtGoodsItem;
import com.ruoyi.system.domain.vo.HtOrderRecordVo;
import com.ruoyi.system.domain.vo.HtOrderSystemVo;
import com.ruoyi.system.domain.vo.HtOrderVo;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.IHtOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-23
 */

@Slf4j
@Service
public class HtOrderServiceImpl extends ServiceImpl<HtOrderMapper, HtOrder> implements IHtOrderService {
    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private HtGoodsMapper goodsMapper;

    @Autowired
    private AppUserMapper appUserMapper;
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private HtOrderRecordMapper htOrderRecordMapper;
    @Autowired
    private HtPaymentAgreementsMapper htPaymentAgreementsMapper;


    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public HtOrder selectHtOrderById(Long id) {
        return htOrderMapper.selectHtOrderById(id);
    }

    /**
     * 查询订单列表
     *
     * @param htOrder 订单
     * @return 订单
     */
    @Override
    public List<HtOrder> selectHtOrderList(HtOrder htOrder) {
        return htOrderMapper.selectHtOrderList(htOrder);
    }

    /**
     * 查询抢购明细列表
     *
     * @param htOrder 查询条件
     * @return 抢购商品列表
     */
    public List<HtOrder> selectHtOrderGrabList(HtOrder htOrder){
        List<HtOrder> list=htOrderMapper.selectHtOrderList(htOrder);
        for(HtOrder data:list){
            HtGoods htGoods= goodsMapper.selectHtGoodsById(data.getGoodsId());
            AppUser appUser=appUserMapper.selectAppUserById(data.getUserId());
            if(StringUtils.isNotNull(appUser)){
                data.setUserName(appUser.getRealName());
            }
            data.setHtGoods(htGoods);
        }
        return list;
    }


    @Override
    public List<HtOrderSystemVo> selectSysHtOrderList(HtOrder htOrder) {
        return htOrderMapper.selectSysHtOrderList(htOrder);
    }

    /**
     * 新增订单
     *
     * @param htOrder 订单
     * @return 结果
     */
    @Override
    public int insertHtOrder(HtOrder htOrder) {
        return htOrderMapper.insertHtOrder(htOrder);
    }

    /**
     * 修改订单
     *
     * @param htOrder 订单
     * @return 结果
     */
    @Override
    public int updateHtOrder(HtOrder htOrder) {
        return htOrderMapper.updateHtOrder(htOrder);
    }

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteHtOrderByIds(Long[] ids) {
        return htOrderMapper.deleteHtOrderByIds(ids);
    }

    /**
     * 删除订单信息
     *
     * @param id 订单主键
     * @return 结果
     */
    @Override
    public int deleteHtOrderById(Long id) {
        return htOrderMapper.deleteHtOrderById(id);
    }

    @Override
    public HashMap<String, Object> getTodayMoney(Integer userId) {
        HashMap<String, Object> map = new HashMap<>();
        List<HtOrderRecord> list = htOrderRecordMapper.getTodayPayMoney(userId);
        map.put("payMoney", list);
        list = htOrderRecordMapper.getTodayRevenueMoney(userId);
        map.put("RevenueMoney", list);
        return map;
    }

    @Override
    public HashMap<String, Object> getPastMoney(Long userId) {
        HashMap<String, Object> res = new HashMap<>();

        List<HtOrderRecordVo> payListVo = new ArrayList<>();
        List<HtOrderRecordVo> revenueListVo = new ArrayList<>();
        HashMap<String, ArrayList> payMap = new HashMap<>();
        HashMap<String, ArrayList> revenueMap = new HashMap<>();
        //支出
        List<HtOrderRecord> payList = htOrderRecordMapper.selectOldPayList(userId);
        if (payList.size() > 0) {
            for (HtOrderRecord htOrder : payList) {
                String orderDate = DateUtils.parseDateToStr("yyyy-MM-dd", htOrder.getCreateTime());
                if (payMap.get(orderDate) != null) {
                    payMap.get(orderDate).add(htOrder);
                } else {
                    ArrayList<HtOrderRecord> list = new ArrayList<>();
                    list.add(htOrder);
                    payMap.put(orderDate, list);
                }
            }
            for (String key : payMap.keySet()) {
                HtOrderRecordVo htOrderVo = new HtOrderRecordVo();
                ArrayList list = payMap.get(key);
                htOrderVo.setDate(key);
                htOrderVo.setOrderList(list);
                payListVo.add(htOrderVo);
            }
        }


        List<HtOrderRecord> revenueList = htOrderRecordMapper.selectRevenueList(userId);
        if (revenueList.size() > 0) {
            for (HtOrderRecord htOrder : revenueList) {
                String orderDate = DateUtils.parseDateToStr("yyyy-MM-dd", htOrder.getCreateTime());
                if (revenueMap.get(orderDate) != null) {
                    revenueMap.get(orderDate).add(htOrder);
                } else {
                    ArrayList<HtOrderRecord> list = new ArrayList<>();
                    list.add(htOrder);
                    revenueMap.put(orderDate, list);
                }
            }
            for (String key : revenueMap.keySet()) {
                HtOrderRecordVo htOrderVo = new HtOrderRecordVo();
                ArrayList list = revenueMap.get(key);
                htOrderVo.setDate(key);
                htOrderVo.setOrderList(list);
                revenueListVo.add(htOrderVo);
            }
        }

        res.put("payListVo", payListVo);
        res.put("revenueListVo", revenueListVo);
        return res;
    }

    @Override
    public HashMap<String, String> payHtOrder(HtOrder htOrder) {
        HashMap<String, String> map = new HashMap<>();
        htOrder.setOrderId(UUID.randomUUID().toString().substring(0, 24));
        htOrder.setOrderStatus(1L);
        htOrder.setMatchedTime(new Date());
        htOrder.setCreatedAt(new Date());
        //判断用户当天是否已下单
        QueryWrapper<HtOrder> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", htOrder.getUserId());
        wrapper.likeRight("created_at", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        List<HtOrder> orderList = htOrderMapper.selectList(wrapper);
        if (!orderList.isEmpty() && orderList.get(0) != null) {
            map.put("status", "1");
            map.put("msg", "您今天已经下单了");
            return map;
        }
        htOrderMapper.insertHtOrder(htOrder);
        map.put("status", "0");
        map.put("msg", "下单成功");
        System.err.println("下单成功");
//        logger.info(htOrder.getRecipientName() + "下单成功");
        return map;
    }

    @Override
    @Transactional
    public int updateOrderType(HtOrder htOrder) {

        HtOrder order = htOrderMapper.selectHtOrderById(htOrder.getId());


        HtGoods goods = goodsMapper.selectOne(Wrappers.<HtGoods>query().eq("id", order.getGoodsId()));


        if (goods == null) {
            return 0;
        }
        HashMap<Integer, List<String>> map = new HashMap<>();
        for (PaymentProof paymentProof : htOrder.getPaymentProofList()) {
            map.put(paymentProof.getOrderRecordId(), paymentProof.getImage());

        }

        List<HtOrder> htOrders = htOrderMapper.selectList(Wrappers.<HtOrder>query()
                .eq("order_type", 3)
                .like("created_at", DateUtils.getDate())
        );
        if (!htOrders.isEmpty()) {
            for (HtOrder htOrder1 : htOrders) {
                HtGoods htGoods = goodsMapper.selectById(htOrder1.getGoodsId());
                htGoods.setUserId(htOrder1.getUserId().intValue());
                goodsMapper.updateById(htGoods);
            }
        }
//        HtOrderRecord orderId = htOrderRecordMapper.selectOne(Wrappers.<HtOrderRecord>query()
//                .eq("order_id", htOrder.getId()));
//        orderId.setPaymentProof(paymentProof);
//        //修改子订单表中的付款凭证
//        htOrderRecordMapper.updateById(orderId);
        if (order.getRecipientId() == 1) {


            //修改订单状态
            order.setOrderStatus(9L);
            List<HtOrderRecord> orderId = htOrderRecordMapper.selectList(Wrappers.<HtOrderRecord>query()
                    .eq("order_id", htOrder.getId()));

            String str = new String();
            for (HtOrderRecord htOrderRecord : orderId) {
                List<String> strings = map.get(htOrderRecord.getId().intValue());
                for (String string : strings) {
                    str += "," + string;
                }

                htOrderRecord.setPaymentProof(str.substring(1, str.length()));
                htOrderRecord.setOrderId(htOrder.getId().intValue());
                htOrderRecordMapper.updateById(htOrderRecord);
            }
            return htOrderMapper.updateById(order);
        }


        List<HtOrderRecord> orderUserList = htOrderRecordMapper.selectList(Wrappers.<HtOrderRecord>query()
                .eq("user_id", order.getUserId())
                .like("create_time", DateUtils.parseDateToStr("yyyy-MM-dd", new Date())));
        // 判断当前用户的需要订单消除
        List<Integer> ids = new ArrayList<Integer>();
        if (!orderUserList.isEmpty()) {
            for (HtOrderRecord htOrderRecord : orderUserList) {
                // 付给第一个人   2

                String str = new String();
                List<String> strings = map.get(htOrderRecord.getId().intValue());
                for (String string : strings) {
                    str += "," + string;
                }
                str = str.substring(1, str.length());
                htOrderRecord.setPaymentProof(str);
                htOrderRecordMapper.updateById(htOrderRecord);

                // 查询收款人的子订单
                List<HtOrderRecord> orderRecipientList = htOrderRecordMapper.selectList(Wrappers.<HtOrderRecord>query()
                        .eq("recipient_id", htOrderRecord.getRecipientId())
                        .like("create_time", DateUtils.parseDateToStr("yyyy-MM-dd", new Date()))
                        .isNull("payment_proof"));
                if (orderRecipientList.isEmpty()) {
                    ids.add(htOrderRecord.getOrderId());
                }
            }
        }

//        htOrderMapper.updateHtOrder(order);

        HtOrder finishOrder = new HtOrder();
        // 修改所有已完成的订单
        for (Integer id : ids) {
            finishOrder.setId(id.longValue());
            finishOrder.setOrderStatus(9l);
            htOrderMapper.updateHtOrder(finishOrder);
        }

        // 当前用户的订单已经结束
        order.setOrderStatus(9L);
        return htOrderMapper.updateHtOrder(order);
    }

    //封装收益
    private HtIncome getHtIncome(HtGoods goods) {
        HtIncome htIncome = new HtIncome();
        htIncome.setGoodsId(goods.getId().intValue());
        htIncome.setUserId(goods.getUserId());
        htIncome.setGoodsImg(goods.getCateId());
        htIncome.setResalePrice(goods.getResalePrice());
        htIncome.setPrice(goods.getPrice());
        htIncome.setGoodsName(goods.getStoreName());
        htIncome.setMyRevenue(goods.getResalePrice().subtract(goods.getPrice()));
        return htIncome;
    }

    @Override
    public HtOrderVo selectOrderById(Long id) {
        HtOrder htOrder = htOrderMapper.selectHtOrderById(id);
        HtOrderVo htOrderVo = new HtOrderVo();
        BeanUtils.copyProperties(htOrder, htOrderVo);

        HtGoods htGoods = goodsMapper.selectHtGoodsById(htOrder.getGoodsId());
        BigDecimal multiply = htGoods.getPrice().multiply(new BigDecimal(0.035));
        multiply = multiply.setScale(0, RoundingMode.HALF_UP);
        htOrderVo.setMoneyNum(multiply);
        htOrderVo.setNum(1);
        htOrderVo.setPrice(htGoods.getResalePrice());
        redisCache.setCacheObject("goods:" + htGoods.getId(), multiply);
        htOrderVo.setGoodsName(htGoods.getStoreName());
        return htOrderVo;
    }

    @Override
    public HashMap<String, Object> getPayInfo(Integer userId) {
        Boolean isPay = true;

        //拿到需要付款的图片
        List<HtOrderRecord> list = htOrderRecordMapper.selectList(Wrappers.<HtOrderRecord>query()
                .eq("user_id", userId)
                .like("create_time", DateUtils.parseDateToStr("yyyy-MM-dd", new Date()))
        );

        if (list.size() == 0) {
            isPay = false;
            list = htOrderRecordMapper.selectList(Wrappers.<HtOrderRecord>query()
                    .eq("recipient_id", userId)
                    .like("create_time", DateUtils.parseDateToStr("yyyy-MM-dd", new Date()))
            );
            for (HtOrderRecord htOrderRecord : list) {
                //当前用户 付款人
                AppUser user = appUserMapper.selectById(htOrderRecord.getUserId());
                htOrderRecord.setUserName(user.getRealName());
                htOrderRecord.setUserPhone(user.getPhone());
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        if (list.size() > 0) {
            for (HtOrderRecord htOrderRecord : list) {
                AppUser user = appUserMapper.selectById(htOrderRecord.getUserId());
                htOrderRecord.setUserName(user.getRealName());
                htOrderRecord.setUserPhone(user.getPhone());
                Integer recipientId = htOrderRecord.getRecipientId();
                HtPaymentAgreements agreements = new HtPaymentAgreements();
                agreements.setMethodId(1L);
                agreements.setUserId(recipientId.longValue());
                List<HtPaymentAgreements> bankList = htPaymentAgreementsMapper.selectHtPaymentAgreementsList(agreements);
                if (bankList.size() > 0) {
                    HtPaymentAgreements bank = bankList.get(0);
                    htOrderRecord.setBank(bank);
                    agreements.setMethodId(2L);
                }


                List<HtPaymentAgreements> wechatList = htPaymentAgreementsMapper.selectHtPaymentAgreementsList(agreements);
                if (wechatList.size() > 0) {
                    HtPaymentAgreements wechat = wechatList.get(0);
                    htOrderRecord.setWechat(wechat);
                    agreements.setMethodId(3L);
                }

                List<HtPaymentAgreements> payList = htPaymentAgreementsMapper.selectHtPaymentAgreementsList(agreements);
                if (payList.size() > 0) {
                    HtPaymentAgreements pay = payList.get(0);
                    htOrderRecord.setPay(pay);
                }
            }
        }
        map.put("isPay", isPay);
        map.put("list", list);
        return map;

    }


    //确认收款
    @Override
    @Transactional
    public int confirmPayment(Integer recordId) {
        HtGoods goodsRes = new HtGoods();
        //当前子订单确认收款
        HtOrderRecord htOrderRecord1 = htOrderRecordMapper.selectById(recordId);
        Integer orderId = htOrderRecord1.getOrderId();
        if (htOrderRecord1.getRecipientId() == 1) {
            htOrderRecord1.setType(1);
            htOrderRecordMapper.updateById(htOrderRecord1);
            List<HtOrderRecord> htOrderRecords = htOrderRecordMapper.selectList(Wrappers.<HtOrderRecord>query()
                    .eq("order_id", orderId)
                    .eq("type", 0));
            if (htOrderRecords.isEmpty()) {

                HtOrder order = htOrderMapper.selectById(orderId);
                order.setId(orderId.longValue());
                if (!(order.getOrderStatus() == 5 || order.getOrderStatus() == 6)) {
                    order.setOrderStatus(3L);
                    htOrderMapper.updateById(order);
                }

                log.error("测试测试测试测试1 {},订单id", "12312312312312312312", orderId.longValue());

                HtGoods goods = goodsMapper.selectOne(Wrappers.<HtGoods>query().eq("id", order.getGoodsId()));
                goodsRes.setUserId(order.getUserId().intValue());
                goodsRes.setId(goods.getId());
                return goodsMapper.updateHtGoods(goodsRes);
            }
        }

        htOrderRecord1.setType(1);
        htOrderRecordMapper.updateById(htOrderRecord1);

        //查询所有相关的子订单状态   如果没有修改主订单状态
        List<HtOrderRecord> htOrderRecords = htOrderRecordMapper.selectList(Wrappers.<HtOrderRecord>query()
                .eq("order_id", orderId)
                .eq("type", 0));
        if (htOrderRecords.isEmpty()) {
            HtOrder order = htOrderMapper.selectHtOrderById(orderId.longValue());
            if (!(order.getOrderStatus() == 5 || order.getOrderStatus() == 6)) {
                order.setOrderStatus(3L);
                htOrderMapper.updateById(order);
            }

            log.error("测试测试测试测试2 {},订单id", "12312312312312312312", orderId.longValue());

            //修改商品表中的持有人
            goodsRes.setUserId(order.getUserId().intValue());
            goodsRes.setId(order.getGoodsId());
            goodsMapper.updateHtGoods(goodsRes);
        }
        //查询是否有收款记录
        List<HtOrderRecord> records = htOrderRecordMapper.selectList(Wrappers.<HtOrderRecord>query()
                .eq("recipient_id", htOrderRecord1.getRecipientId())
                .like("create_time", DateUtils.parseDateToStr("yyyy-MM-dd", new Date()))
                .eq("type", 0));

        if (records.isEmpty()) {
            HtOrder order = htOrderMapper.selectOne(Wrappers.<HtOrder>query()
                    .eq("user_id", htOrderRecord1.getRecipientId())
                    .like("created_at", DateUtils.parseDateToStr("yyyy-MM-dd", new Date())));
            if (order != null) {
                if (!(order.getOrderStatus() == 5 || order.getOrderStatus() == 6)) {
                    order.setOrderStatus(3L);
                    htOrderMapper.updateById(order);
                }
                log.error("测试测试测试测试3 {},订单id", "12312312312312312312", orderId.longValue());


                //修改商品表中的持有人
                goodsRes.setUserId(order.getUserId().intValue());
                goodsRes.setId(order.getGoodsId());
                goodsMapper.updateHtGoods(goodsRes);
            }

        }

        //修改订单状态为已付款
        return 1;

    }

    @Override
    public List<HtGoodsItem> selectGrabOrderList(HtOrder htOrder) {
        // 构建查询条件
        HtGoods query = new HtGoods();
        query.setIsBest(1);
        query.setIsNew(1);
        // 查询商品列表（这里会进行分页）
        List<HtGoods> goodsList = goodsMapper.selectHtGoodsList(query);
        if (goodsList == null || goodsList.isEmpty()) {
            return new ArrayList<>();
        }
        // 获取商品ID列表
        List<Long> goodsIds = goodsList.stream()
                .map(HtGoods::getId)
                .collect(Collectors.toList());
        // 查询今日订单列表（只查询相关商品的订单）
        HtOrder orderQuery = new HtOrder();
        orderQuery.setCreatedAt(new Date());
        orderQuery.setGoodsIds(goodsIds);
        List<HtOrderSystemVo> orderList = htOrderMapper.selectHtOrderListAndSysUser(orderQuery);
        // 构建商品ID到用户名的映射
        Map<Integer, String> goodsIdToUserName = orderList.stream()
                .filter(order -> order.getGoodsId() != null && order.getRealName() != null)
                .collect(Collectors.toMap(
                        HtOrderSystemVo::getGoodsId,
                        HtOrderSystemVo::getRealName,
                        (existing, replacement) -> existing
                ));
        // 转换为HtGoodsItem并设置用户名
        return goodsList.stream()
                .map(goods -> {
                    HtGoodsItem item = new HtGoodsItem();
                    BeanUtils.copyProperties(goods, item);
                    item.setUserName(goodsIdToUserName.get(goods.getId().intValue()));
                    return item;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<AppUser> grabUser() {
        HtOrder orderQuery = new HtOrder();
        orderQuery.setCreatedAt(new Date());
        List<HtOrderSystemVo> orderList = htOrderMapper.selectHtOrderListAndSysUser(orderQuery);
        if (orderList.size() == goodsMapper.selectCount(Wrappers.<HtGoods>query().ne("id", 0).eq("is_best", 1).eq("is_new", 1).eq("is_del", 0))) {
            return null;
        } else {
            List<AppUser> appUsers = appUserMapper.selectList(null);
            // 获取已下单用户的ID列表
            List<Integer> orderedUserIds = orderList.stream()
                    .map(HtOrderSystemVo::getUserId)
                    .collect(Collectors.toList());

            // 过滤出未下单的用户
            return appUsers.stream()
                    .filter(user -> !orderedUserIds.contains(user.getId().intValue()))
                    .collect(Collectors.toList());
        }
    }
}


