package com.ruoyi.system.domain;

//添加mybatisplus的三个包引用

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

//如果自己的包名修改了，则需要改成对应的包名
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单对象 ht_order
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@Data
@TableName("ht_order")
public class HtOrder implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    private Integer recipientId;
    /**
     * 收款人姓名
     */
    @Excel(name = "收款人姓名")
    private String recipientName;

    /**
     * 收款人联系电话
     */
    @Excel(name = "收款人联系电话")
    private String recipientPhone;


    /**
     * 付款金额
     */
    @Excel(name = "付款金额")
    private BigDecimal amount;

    /**
     * 商品金额
     */
    private BigDecimal goodsPrice;

    /**
     * 匹配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "匹配时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date matchedTime;

    /**
     * 付款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "付款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentTime;

    /**
     * 开户银行
     */
    @Excel(name = "开户银行")
    private String bankName;

    /**
     * 银行卡号
     */
    @Excel(name = "银行卡号")
    private String bankAccount;

    /**
     * 微信收款二维码图片地址
     */
    @Excel(name = "微信收款二维码图片地址")
    private String wechatQrCode;

    /**
     * 支付宝收款二维码图片地址
     */
    @Excel(name = "支付宝收款二维码图片地址")
    private String alipayQrCode;

    /**
     * 付款凭证图片地址
     */
    @Excel(name = "付款凭证图片地址")
    private String paymentProof;

    /**
     * 订单状态 012 未付款 付款中 已付款
     */
    @Excel(name = "订单状态 012 未付款 付款中 已付款")
    private Long orderStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    private Long userId;

    /**
     * 用户id
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 商品id
     */
    @Excel(name = "商品id")
    private Long goodsId;
    /**
     * 订单id
     */
    @Excel(name = "订单id")
    private String orderId;

    private Integer orderType;

    @TableField(exist = false)
    private HtGoods htGoods;

    @TableField(exist = false)
    private List<PaymentProof> paymentProofList;

    @TableField(exist = false)
    private List<Long> goodsIds;
}

