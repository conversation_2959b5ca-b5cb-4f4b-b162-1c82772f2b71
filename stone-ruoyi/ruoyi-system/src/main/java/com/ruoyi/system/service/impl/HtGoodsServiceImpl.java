package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.RedisLockUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.IHtGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.UUID;


/**
 * 商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@Service
public class HtGoodsServiceImpl extends ServiceImpl<HtGoodsMapper, HtGoods> implements IHtGoodsService {
    @Autowired
    private HtGoodsMapper htGoodsMapper;
    @Autowired
    private HtOrderMapper htOrderMapper;
    @Autowired
    private AppUserMapper appUserMapper;
    @Autowired
    private HtPaymentAgreementsMapper htPaymentAgreementsMapper;
    @Autowired
    private HtOrderRecordMapper htOrderRecordMapper;
    @Autowired
    private HtGoodsMapper goodsMapper;
    @Autowired
    private SpiltOrder spiltOrder;
    @Autowired
    private RedisLockUtils redisLockUtils;
    @Autowired
    private ComputingConfigUtils computingConfigUtils;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询商品
     *
     * @param id 商品主键
     * @return 商品
     */
    @Override
    public HtGoods selectHtGoodsById(Long id) {
        return htGoodsMapper.selectHtGoodsById(id);
    }

    /**
     * 查询商品列表
     *
     * @param htGoods 商品
     * @return 商品
     */
    @Override
    public List<HtGoods> selectHtGoodsList(HtGoods htGoods) {
        if (htGoods.getIsBest() != null) {
            if (htGoods.getIsBest() != 0) {
                htGoods.setIsShow(1);
            }
        }
        if (htGoods.getIsNew() != null) {
            if (htGoods.getIsNew() != 0) {
                htGoods.setIsShow(1);
            }
        }
        if (htGoods.getActivityId() != null) {
            htGoods.setIsShow(1);
        }
        if (htGoods.getType() == null) {
            htGoods.setType(1);
        }
        List<HtGoods> list = htGoodsMapper.selectHtGoodsList(htGoods);
        if (htGoods.getType() == 1) {
            list.sort(Comparator.comparing(HtGoods::getPrice).reversed());
        } else {
            list.sort(Comparator.comparing(HtGoods::getPrice));
        }

        return list;
    }

    /**
     * 新增商品
     *
     * @param htGoods 商品
     * @return 结果
     */
    @Override
    public int insertHtGoods(HtGoods htGoods) {
        BigDecimal price = htGoods.getPrice();
        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();
        // 售价 1w    转售价 = 售价 + 5%
        htGoods.setResalePrice(price.multiply((new BigDecimal("1").add(htFeeRevenueConfig.getFeeAmount()))).setScale(0, RoundingMode.HALF_UP));
        htGoods.setCreateTime(DateUtils.getNowDate());
        return htGoodsMapper.insertHtGoods(htGoods);
    }

    /**
     * 修改商品
     *
     * @param htGoods 商品
     * @return 结果
     */
    @Override
    public int updateHtGoods(HtGoods htGoods) {
        BigDecimal price = htGoods.getPrice();
        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();
        // 售价 1w    转售价 = 售价 + 5%
        htGoods.setResalePrice(price.multiply((new BigDecimal("1").add(htFeeRevenueConfig.getFeeAmount()))).setScale(0, RoundingMode.HALF_UP));
        htGoods.setUpdateTime(DateUtils.getNowDate());
        return htGoodsMapper.updateHtGoods(htGoods);
    }

    /**
     * 批量删除商品
     *
     * @param ids 需要删除的商品主键
     * @return 结果
     */
    @Override
    public int deleteHtGoodsByIds(Long[] ids) {
        return htGoodsMapper.deleteHtGoodsByIds(ids);
    }

    /**
     * 删除商品信息
     *
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public int deleteHtGoodsById(Long id) {
        return htGoodsMapper.deleteHtGoodsById(id);
    }

    @Override
    @Transactional
    public int updateHtGoodsStatus(HtGoods htGoods) {
        HtGoods goods = htGoodsMapper.selectHtGoodsById(htGoods.getId());
        if (goods.getUserId() != null && !goods.getUserId().equals(htGoods.getUserId())) {
            return 0;
        }
        if (goods.getIsShow() == 1) {
            return 0;
        }

        goods.setIsShow(1);

        goods.setActivityId(1L);


        return htGoodsMapper.updateHtGoods(goods);
    }


    @Override
    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public AjaxResult snappedGoods(Integer userId, Integer goodsId) {
        // 生成唯一的请求标识，用于锁的持有者验证
        String requestId = UUID.randomUUID().toString();

        try {
            // 修改锁的实现，只使用商品ID作为锁的键，确保对同一商品的互斥访问
            boolean locked = redisLockUtils.lock("GOODS_" + goodsId.toString(), requestId);
            if (!locked) {
                return AjaxResult.error("当前商品抢购人数过多，请稍后再试");
            }

            // 获取当前时间 是否在配置的时间范围内
            LocalTime currentTime = LocalTime.now();

            // 从配置中获取时间范围
            HtFeeRevenueConfig config = computingConfigUtils.getHtFeeRevenueConfig();
            LocalTime startTime;
            LocalTime endTime;

            if (config != null && config.getStartTime() != null && config.getEndTime() != null) {
                startTime = LocalTime.parse(config.getStartTime());
                endTime = LocalTime.parse(config.getEndTime());
            } else {
                // 默认时间范围 9:10-9:20
                startTime = LocalTime.of(9, 10);
                endTime = LocalTime.of(9, 20);
            }

            if (currentTime.isAfter(startTime) && currentTime.isBefore(endTime)) {
                return AjaxResult.error(9999, "还未到抢购时间，请在" + endTime + "开始抢购");
            }
            if (redisCache.getCacheList("testPrice").isEmpty()) {
                //查询
                List<HtGoods> htGoods = goodsMapper.selectList(Wrappers.<HtGoods>query()
                        .eq("is_del", 0)
                        .eq("user_id", 1)
                        .ne("id", 0)
                        .eq("is_show", 1));
                if (!htGoods.isEmpty()) {
                    redisCache.setCacheList("testPrice", htGoods);
                }
            }

            // 使用悲观锁方式：先查询后在事务中更新
            HtGoods htGoods = htGoodsMapper.selectById(goodsId);
            if (htGoods == null) {
                return AjaxResult.error("商品不存在");
            }

            if (htGoods.getIsShow() == 0) {
                return AjaxResult.error("商品已抢完");
            }

            //判断用户当天是否已下单
            QueryWrapper<HtOrder> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId);
            wrapper.likeRight("created_at", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
            List<HtOrder> orderList = htOrderMapper.selectList(wrapper);
//            if (!orderList.isEmpty() && orderList.get(0) != null) {
//                return AjaxResult.warn("您今天已经抢购过了");
//            }

            //封装order对象
            HtOrder order = getOrder(userId, htGoods);

            // 使用乐观锁方式：条件更新，确保只有商品状态为可见时才能更新成功
            htGoods.setIsShow(0);
            int updateCount = htGoodsMapper.update(htGoods,
                    Wrappers.<HtGoods>update()
                            .eq("id", goodsId)
                            .eq("is_show", 1));  // 只有商品状态为1(可见)时才能更新成功

            if (updateCount <= 0) {
                // 更新失败，说明商品已被抢走
                return AjaxResult.error("商品已被抢购");
            }

            int i1 = htOrderMapper.insertHtOrder(order);
            //创建空订单
            if (i1 > 0) {
                List<HtGoods> userGoodsList = goodsMapper.selectList(Wrappers.<HtGoods>query()
                        .eq("user_id", userId)
                        .eq("is_del", 0));
                if (userGoodsList.isEmpty()) {
                    String str = redisCache.getCacheObject("test");

                    if (str == null) {
                        redisCache.setCacheObject("test", userId + ",");
                    } else {
                        redisCache.setCacheObject("test", str + userId + ",");
                    }
                }
                //只有首次买平台石头的时候
                if (htGoods.getUserId() == 1 && userGoodsList.isEmpty()) {

                    //封装订单记录表
                    HtOrderRecord htOrderRecord = new HtOrderRecord();
                    htOrderRecord.setOrderId(order.getId().intValue());
                    htOrderRecord.setId(order.getId().intValue());
                    htOrderRecord.setRecipientName(order.getRecipientName());
                    htOrderRecord.setRecipientId(order.getRecipientId());
                    htOrderRecord.setRecipientPhone(order.getRecipientPhone());
                    htOrderRecord.setAmount(htGoods.getPrice());
                    htOrderRecord.setUserId(order.getUserId().intValue());
                    htOrderRecord.setCreateTime(order.getCreatedAt());
                    htOrderRecordMapper.insert(htOrderRecord);
                } else {
                    //每次用户抢购完 查看石头是否被抢购完 如果抢购完则需要走 算法逻辑
                    List<HtGoods> goodsList = htGoodsMapper.selectList(Wrappers.<HtGoods>query()
                            .eq("is_show", 1)
                            .eq("is_del", 0)
                            .eq("activity_id", htGoods.getActivityId()));
                    if (goodsList.isEmpty()) {
                        List<HtOrder> orderList1 = htOrderMapper.selectList(Wrappers.<HtOrder>query()
                                .eq("order_type", 3)
                                .eq("created_at", DateUtils.getDate()));
                        if (!orderList1.isEmpty()) {
                            for (HtOrder htOrder : orderList1) {
                                HtGoods htGoods1 = new HtGoods();
                                htGoods1.setUserId(htOrder.getUserId().intValue());
                                htGoods1.setId(htOrder.getGoodsId());
                                htGoodsMapper.updateHtGoods(htGoods1);
                            }
                        }

                        //todo
                        spiltOrder.splitOrder();

                        return AjaxResult.success("开始执行算法", 8888);
                    }
                }

                return AjaxResult.success("抢购成功");
            }
            return AjaxResult.error("抢购失败");
        } finally {
            // 释放锁
            redisLockUtils.unlock("GOODS_" + goodsId.toString());
        }
    }



    @Override
    public List<HtGoods> selectSystemHtGoodsList(HtGoods htGoods) {
        List<HtGoods> list = htGoodsMapper.selectSystemHtGoodsList(htGoods);

        return list;
    }

    @Override
    public int insertSystemHtGoods(HtGoods htGoods) {
        htGoods.setUserId(SecurityUtils.getUserId().intValue());
        BigDecimal price = htGoods.getPrice();
        htGoods.setActivityId(5L);

        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();
        // 售价 1w    转售价 = 售价 + 5%
        htGoods.setResalePrice(price.multiply((new BigDecimal("1").add(htFeeRevenueConfig.getFeeAmount()))).setScale(0, RoundingMode.HALF_UP));
        htGoods.setCreateTime(DateUtils.getNowDate());
        return htGoodsMapper.insertHtGoods(htGoods);
    }

    @Override
    public AjaxResult snappedGoods1() {
        spiltOrder.splitOrder();
        return null;
    }

    @Override
    public List<HtGoods> selectMyHtGoodsList(Long userId) {
        List<HtGoods> list = htGoodsMapper.selectMyHtGoodsList(userId);
        return list;
    }

    public HtOrder getOrder(Integer userId, HtGoods htGoods) {
        HtOrder htOrder = new HtOrder();
        htOrder.setGoodsId(htGoods.getId());
        htOrder.setOrderStatus(1L);
        htOrder.setGoodsPrice(htGoods.getPrice());
        htOrder.setRecipientId(htGoods.getUserId());
        htOrder.setUserId(Long.valueOf(userId));
//        AppUser user = appUserMapper.selectById(htGoods.getUserId());
        AppUser user = appUserMapper.selectAppUserById(htGoods.getUserId().longValue());
        htOrder.setRecipientName(user.getRealName());
        htOrder.setRecipientPhone(user.getPhone());
        htOrder.setCreatedAt(new Date());
        htOrder.setUpdatedAt(new Date());
        htOrder.setOrderId(UUID.randomUUID().toString().substring(0, 24));
        //todo 判断那问题
        List<HtGoods> userGoodsList = goodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("user_id", userId)
                .eq("is_del", 0));
        //判断是否是第一批买平台的石头
        if (htGoods.getUserId() != 1) {

            HtGoods userGoods = new HtGoods();
            BigDecimal sum = new BigDecimal(0);
            for (HtGoods goods : userGoodsList) {
                sum = sum.add(goods.getPrice());
            }
            userGoods.setPrice(sum);
            BigDecimal sub = new BigDecimal(0);
            if (userGoodsList.isEmpty()) {
                htOrder.setAmount(htGoods.getPrice());
                htOrder.setOrderType(1);
                return htOrder;
            } else {
                sub = userGoods.getPrice().subtract(htGoods.getPrice());
                htOrder.setAmount(sub.abs());
                if (userGoods.getPrice().compareTo(htGoods.getPrice()) < 0) {
                    htOrder.setOrderType(1);
                }
                if (userGoods.getPrice().compareTo(htGoods.getPrice()) == 0) {
                    //todo  双方石头价格一样 怎么做处理呢
                    htOrder.setOrderStatus(3L);
                    htOrder.setOrderType(3);


                }
                if (userGoods.getPrice().compareTo(htGoods.getPrice()) > 0) {
                    htOrder.setOrderType(0);
                }
                return htOrder;
            }

        }
        if (!userGoodsList.isEmpty()) {
            HtGoods userGoods = new HtGoods();
            BigDecimal sum = new BigDecimal(0);
            for (HtGoods goods : userGoodsList) {
                sum = sum.add(goods.getPrice());
            }
            userGoods.setPrice(sum);
            BigDecimal sub = new BigDecimal(0);
            sub = userGoods.getPrice().subtract(htGoods.getPrice());
            htOrder.setAmount(sub.abs());
            if (userGoods.getPrice().compareTo(htGoods.getPrice()) < 0) {
                htOrder.setOrderType(1);
            }
            if (userGoods.getPrice().compareTo(htGoods.getPrice()) == 0) {
                //todo  双方石头价格一样 怎么做处理呢
                htOrder.setOrderStatus(3L);
                htOrder.setOrderType(3);


            }
            if (userGoods.getPrice().compareTo(htGoods.getPrice()) > 0) {
                htOrder.setOrderType(0);
            }
            return htOrder;
        }

        htOrder.setOrderStatus(2L);

        return htOrder;
    }
}

