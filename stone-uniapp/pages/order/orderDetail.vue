<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="header">
			<text class="title">委托寄售</text>
		</view>
		<!-- 商品信息 -->
		<view class="order-info">
			<view class="info-row">
				<text class="label">商品名称：</text>
				<text class="value">{{ storeName }}</text>
			</view>
			<view class="info-row">
				<text class="label">原价：</text>
				<text class="value">¥ {{ price }}</text>
			</view>
			<view class="info-row">
				<text class="label">现价：</text>
				<text class="value">¥ {{ order.price }}</text>
			</view>
			<view class="info-row">
				<text class="label">数量：</text>
				<text class="value">1</text>
			</view>

			<view>
				<view>收款方式：</view>
				<view style="border: 1rpx solid #000; width: 650rpx;padding: 20rpx;margin-top: 20rpx;">
					<view>银行名称：{{bank.bankName}}</view>
					<view>银行卡号：{{bank.accountInfo }}</view>
				</view>
				<view @cllick="previewImg(1)">
					<view>微信收款码：</view>
					<view style="width: 200rpx;height:200rpx;">
						<img :src="chat.chatImg" @click="check" style="widtn:100%;height:100%;" alt="">
					</view>
				</view>
				<view @cllick="previewImg(2)">
					<view>支付宝收款码：</view>
					<view style="width: 200rpx;height:200rpx;">
						<img :src="alipay.alipayImg" @click="check2" style="widtn:100%;height:100%;" alt="">
					</view>
				</view>
			</view>
		</view>

		<!-- 手续费说明 -->
		<view class="fee-info">
			<text class="fee-text">根据《寄售规则》您需支付寄售价格的 3.5% 作为手续费：</text>
			<text class="fee-detail">委托价格:¥{{ order.price }}，需支付手续费:¥{{ order.moneyNum }}</text>
		</view>

		<!-- 支付方式 -->
		<view class="payment-options">
			<view class="option-row">
				<checkbox v-if="balance!=0" :checked="paymentOptions.balance" @click="useBalance" /> 可用余额：￥{{ balance }}
			</view>
			<view class="text" v-if="paymentOptions.balance">已抵扣：-￥ <text
					style="color: red;">{{ balance>=this.order.moneyNum?this.order.moneyNum:balance }}</text>
			</view>
			<view class="text" v-if="paymentOptions.balance">需付款：￥<text
					style="color: red;">{{this.order.moneyNum -balance<0?0:this.order.moneyNum -balance}}</text></view>
			<view class="option-row" v-if="flag!=2">
				<view class="left">
					付款凭证：
				</view>
				<view class="right">
					<u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" multiple></u-upload>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="action">
			<button class="submit-btn" @click="submitOrder" :loading="submitLoading">确认发布</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				order: {},
				paymentOptions: {
					balance: false,
				},
				balance: '',
				userInfo: {},
				fileList: [],
				storeName: '',
				price: '',
				bank: {},
				chat: {},
				alipay: {},
				flag: 0,
				submitLoading:false,
			};
		},
		onLoad(options) {
			console.log(options.id);
			this.api_get(options.id)
			this.userInfo = uni.getStorageSync('userInfo')
			this.storeName = options.storename
			this.price = options.price
			this.api_getpay()
			this.getuserInfo()
		},
		methods: {
			getuserInfo() {
				this.$api.request({
					url: this.$api.getuserInfo + `/${this.userInfo.id}`
				}).then((res) => {
					this.balance = res.data.balance
				})
			},
			goBack() {
				uni.navigateBack();
			},
			api_get(id) {
				this.$api.request({
					url: this.$api.getseildetial + `/${id}`
				}).then((res) => {
					console.log(res, '数据');
					this.order = res.data
				})
			},

			api_getpay() {
				this.$api.request({
					url: this.$api.getpayways + '?userId=1'
				}).then((res) => {
					console.log(res.rows, '付款方式');
					this.bank = res.rows.filter(item => {
						return item.methodId == 1
					})[0]
					this.chat = res.rows.filter(item => {
						return item.methodId == 2
					})[0]
					this.alipay = res.rows.filter(item => {
						return item.methodId == 3
					})[0]
				})
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				let filePath
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					let result = await this.$api.uploadFile({
						url: this.$api.upPic,
						filePath: lists[i].url
					});
					result = JSON.parse(result);
					let item = this.fileList[fileListLen]
					this.fileList.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result.url
					}))
					fileListLen++
				}
			},
			
			check() {
				let imgsArray = [];
				imgsArray[0] = this.chat.chatImg;
				console.log('imgsArray[0]', imgsArray[0])
				uni.previewImage({
					current: 0,
					urls: imgsArray,
				});
			},
			check2() {
				let imgsArray = [];
				imgsArray[0] = this.alipay.alipayImg;
				console.log('imgsArray[0]', imgsArray[0])
				uni.previewImage({
					current: 0,
					urls: imgsArray,
				});
			},
			// 删除图片
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
			// 使用余额
			useBalance(e) {
				this.paymentOptions.balance = !this.paymentOptions.balance
				if (this.paymentOptions.balance == false) {
					this.flag = 0
				} else {
					if (this.balance >= this.order.moneyNum) {
						// this.order.moneyNum = this.order.moneyNum - this.balance
						this.flag = 2
					} else {
						this.flag = 1
					}
				}
			},
			submitOrder() {
				// 数据验证
				if (!this.validateOrderData()) {
					return;
				}

				this.submitLoading = true;
				console.log("提交订单信息：", this.paymentOptions);

				// 计算支付类型标志
				this.calculatePaymentFlag();

				uni.showModal({
					title: '操作通知',
					content: '是否确认发布?',
					success: (res) => {
						if (res.confirm) {
							this.processOrderSubmission();
						} else {
							// 用户取消，重置loading状态
							this.submitLoading = false;
						}
					},
					fail: () => {
						// 弹窗失败，重置loading状态
						this.submitLoading = false;
					}
				});
			},

			// 验证订单数据
			validateOrderData() {
				if (!this.order || !this.order.id) {
					uni.showToast({
						title: "订单信息不完整",
						icon: 'none'
					});
					return false;
				}

				if (!this.userInfo || !this.userInfo.id) {
					uni.showToast({
						title: "用户信息不完整",
						icon: 'none'
					});
					return false;
				}

				return true;
			},

			// 计算支付类型标志
			calculatePaymentFlag() {
				if (this.paymentOptions.balance == false) {
					this.flag = 0;
				} else {
					if (this.balance >= this.order.moneyNum) {
						this.flag = 2;
					} else {
						this.flag = 1;
					}
				}
			},

			// 处理订单提交
			processOrderSubmission() {
				console.log(this.fileList, 'fileList');

				// 检查是否需要上传支付凭证
				if (this.flag != 2 && this.fileList.length == 0) {
					uni.showToast({
						title: "请上传支付凭证",
						icon: 'none'
					});
					this.submitLoading = false;
					return;
				}

				// 准备请求数据
				const requestData = this.prepareRequestData();

				// 发送请求
				this.sendSubmitRequest(requestData);
			},

			// 准备请求数据
			prepareRequestData() {
				const baseData = {
					orderId: this.order.id,
					userId: this.userInfo.id,
					goodsId: this.order.goodsId,
					price: this.order.moneyNum,
					type: this.flag,
				};

				// 如果不是余额支付且有上传图片，添加图片信息
				if (this.flag != 2 && this.fileList.length > 0) {
					const imgs = this.fileList.map(item => item.url).join(',');
					baseData.uploadImg = imgs;
					console.log(imgs, '修改的图片格式');
				}

				return baseData;
			},

			// 发送提交请求
			sendSubmitRequest(requestData) {
				this.$api.request({
					url: this.$api.putmessionon,
					method: "POST",
					data: requestData
				}).then((res) => {
					this.handleSubmitSuccess(res);
				}).catch((error) => {
					this.handleSubmitError(error);
				}).finally(() => {
					this.submitLoading = false;
				});
			},

			// 处理提交成功
			handleSubmitSuccess(res) {
				console.log('提交响应:', res);
				if (res.code == 200) {
					uni.showToast({
						title: "发布成功",
						icon: "success"
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					// 服务器返回错误
					uni.showToast({
						title: res.msg || "发布失败，请重试",
						icon: 'none'
					});
				}
			},

			// 处理提交错误
			handleSubmitError(error) {
				console.error('提交订单失败:', error);
				uni.showToast({
					title: "网络错误，请检查网络连接后重试",
					icon: 'none'
				});
			},

		},
	};
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #3cb371;
		padding: 10rpx 20rpx;
		color: white;
	}

	.title {
		font-size: 32rpx;
	}

	.order-info,
	.fee-info,
	.payment-options {
		background-color: white;
		padding: 20rpx;
		margin-top: 20rpx;
		border-radius: 10rpx;
	}

	.info-row,
	.option-row {
		display: flex;
		justify-content: space-between;
		margin-top: 50rpx;
		margin-bottom: 10rpx;
	}

	.label {
		color: #555;
		font-size: 28rpx;
	}

	.value {
		color: #333;
		font-size: 28rpx;
	}

	.fee-text {
		color: #f00;
		font-size: 28rpx;
	}

	.fee-detail {
		color: #666;
		margin-top: 10rpx;
	}

	.submit-btn {
		width: 100%;
		background-color: #3cb371;
		color: white;
		padding: 15rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
		margin-top: 20rpx;
	}

	.text {
		height: 60rpx;
		margin-top: 10rpx;
		line-height: 60rpx;
		padding-left: 30rpx;
		font-weight: bold;
	}
</style>